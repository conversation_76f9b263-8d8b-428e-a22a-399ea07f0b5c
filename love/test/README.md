# Love Website 测试中心

## 📋 测试列表

### � R2视频加载测试
**文件**: `r2-video-loading-test.html`
**访问**: https://love.yuh.cool/test/r2-video-loading-test.html

**功能**:
- 测试R2存储桶视频加载功能
- 串行加载5个视频，解决并发冲突问题
- 独立VideoLoader实例，避免状态冲突

**操作**:
- 🚀 开始串行测试：测试所有视频
- 🔄 重置测试：重置状态
- 🎯 测试第一个：单独测试调试

**状态**: ✅ 已完成 - 所有视频正常加载

### 🌩️ Cloudinary视频加载测试
**文件**: `cloudinary-video-loading-test.html`
**访问**: https://love.yuh.cool/test/cloudinary-video-loading-test.html

**功能**:
- 测试Cloudinary多账户的视频加载功能
- 使用CLOUDINARY_ONLY模式验证第二层CDN
- 验证5个账户(YU0-YU4)的映射正确性
- 串行测试避免并发冲突，独立VideoLoader实例
- 实时性能监控和详细错误报告

**操作**:
- 🚀 开始串行测试：测试所有5个页面视频
- 🎯 测试第一个：单独测试首页视频调试
- 🔄 重置测试：重置状态和结果

**账户映射**:
- 🏠 Home → YU0 (dcglebc2w)
- 💕 Anniversary → YU1 (drhqbbqxz)
- 👫 Meetings → YU2 (dkqnm9nwr)
- 🌟 Memorial → YU3 (ds14sv2gh)
- 💑 Together Days → YU4 (dpq95x5nf)

**技术特性**:
- 参考R2测试页面的设计风格和测试方式
- 独立VideoLoader实例避免状态冲突
- 串行测试机制，每个视频间隔1秒
- 完整的错误处理和状态反馈

**状态**: 🔄 待测试 - 验证第二层CDN性能

---

## 🔧 测试开发指南

### 添加新测试
1. 在 `/test` 目录创建HTML文件
2. 使用描述性文件名，如 `功能-测试类型-test.html`
3. 在此README中添加测试说明

### 测试规范
- 包含详细的控制台日志
- 提供清晰的操作按钮
- 显示实时状态和结果
- 支持重置和重新测试

### 访问方式
所有测试页面通过以下方式访问：
```
https://love.yuh.cool/test/[文件名].html
```
